import React, { useState } from 'react';
import { Plus, Search, Edit, Trash2, Package, Filter } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const Products = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  // Sample products data
  const products = [
    {
      id: 1,
      name: 'iPhone 14 Pro',
      barcode: '123456789',
      category: 'هواتف ذكية',
      supplier: 'TechSource',
      purchasePrice: 800,
      salePrice: 999,
      stock: 25,
      image: '/api/placeholder/60/60'
    },
    {
      id: 2,
      name: 'Samsung Galaxy S23',
      barcode: '987654321',
      category: 'هواتف ذكية',
      supplier: 'Gulf Electronics',
      purchasePrice: 700,
      salePrice: 899,
      stock: 18,
      image: '/api/placeholder/60/60'
    },
    {
      id: 3,
      name: 'MacBook Air M2',
      barcode: '456789123',
      category: 'أجهزة كمبيوتر',
      supplier: 'AlphaDistributors',
      purchasePrice: 1000,
      salePrice: 1299,
      stock: 12,
      image: '/api/placeholder/60/60'
    },
    {
      id: 4,
      name: 'iPad Pro 12.9',
      barcode: '789123456',
      category: 'أجهزة لوحية',
      supplier: 'TechSource',
      purchasePrice: 900,
      salePrice: 1199,
      stock: 8,
      image: '/api/placeholder/60/60'
    },
    {
      id: 5,
      name: 'AirPods Pro',
      barcode: '321654987',
      category: 'إكسسوارات',
      supplier: 'ElectroGoods',
      purchasePrice: 180,
      salePrice: 249,
      stock: 35,
      image: '/api/placeholder/60/60'
    },
    {
      id: 6,
      name: 'Apple Watch Series 8',
      barcode: '654987321',
      category: 'ساعات ذكية',
      supplier: 'TechSource',
      purchasePrice: 300,
      salePrice: 399,
      stock: 5,
      image: '/api/placeholder/60/60'
    }
  ];

  const categories = ['all', 'هواتف ذكية', 'أجهزة كمبيوتر', 'أجهزة لوحية', 'إكسسوارات', 'ساعات ذكية'];

  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.barcode.includes(searchTerm);
    const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const getStockStatus = (stock) => {
    if (stock === 0) return { color: 'bg-red-100 text-red-800', text: 'نفد المخزون' };
    if (stock < 10) return { color: 'bg-orange-100 text-orange-800', text: 'مخزون منخفض' };
    return { color: 'bg-green-100 text-green-800', text: 'متوفر' };
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">إدارة المنتجات</h1>
        <Button className="bg-blue-600 hover:bg-blue-700">
          <Plus className="w-4 h-4 ml-2" />
          إضافة منتج جديد
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="w-5 h-5 absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="البحث بالاسم أو الباركود..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-4 pr-10 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                dir="rtl"
              />
            </div>
            <div className="flex gap-2">
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">جميع الفئات</option>
                {categories.slice(1).map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>
              <Button variant="outline">
                <Filter className="w-4 h-4 ml-2" />
                تصفية
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Products Table */}
      <Card>
        <CardHeader>
          <CardTitle className="text-right">قائمة المنتجات ({filteredProducts.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full text-right">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-right py-3 px-4 font-medium text-gray-700">المنتج</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-700">الباركود</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-700">الفئة</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-700">المورد</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-700">سعر الشراء</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-700">سعر البيع</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-700">المخزون</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-700">الحالة</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-700">الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {filteredProducts.map((product) => {
                  const stockStatus = getStockStatus(product.stock);
                  return (
                    <tr key={product.id} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-4 px-4">
                        <div className="flex items-center gap-3">
                          <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                            <Package className="w-6 h-6 text-gray-500" />
                          </div>
                          <div>
                            <p className="font-medium">{product.name}</p>
                            <p className="text-sm text-gray-500">#{product.id}</p>
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-4 text-gray-600">{product.barcode}</td>
                      <td className="py-4 px-4 text-gray-600">{product.category}</td>
                      <td className="py-4 px-4 text-gray-600">{product.supplier}</td>
                      <td className="py-4 px-4 text-gray-600">${product.purchasePrice}</td>
                      <td className="py-4 px-4 font-medium">${product.salePrice}</td>
                      <td className="py-4 px-4">
                        <span className={`px-2 py-1 rounded-full text-sm font-medium ${
                          product.stock > 20 ? 'bg-green-100 text-green-800' :
                          product.stock > 10 ? 'bg-yellow-100 text-yellow-800' :
                          product.stock > 0 ? 'bg-orange-100 text-orange-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {product.stock}
                        </span>
                      </td>
                      <td className="py-4 px-4">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${stockStatus.color}`}>
                          {stockStatus.text}
                        </span>
                      </td>
                      <td className="py-4 px-4">
                        <div className="flex gap-2">
                          <Button variant="ghost" size="sm">
                            <Edit className="w-4 h-4" />
                          </Button>
                          <Button variant="ghost" size="sm" className="text-red-600 hover:text-red-700">
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Products;

