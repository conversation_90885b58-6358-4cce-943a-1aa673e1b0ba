import React, { useState } from 'react';
import { 
  Home, 
  Package, 
  ShoppingCart, 
  Users, 
  FileText, 
  Settings,
  BarChart3,
  Truck,
  DollarSign
} from 'lucide-react';
import Sidebar from './components/Sidebar';
import Header from './components/Header';
import Dashboard from './components/Dashboard';
import Products from './components/Products';
import Sales from './components/Sales';
import './App.css';

function App() {
  const [activeTab, setActiveTab] = useState('dashboard');

  const menuItems = [
    { id: 'dashboard', label: 'لوحة التحكم', icon: Home },
    { id: 'products', label: 'المنتجات', icon: Package },
    { id: 'sales', label: 'المبيعات', icon: ShoppingCart },
    { id: 'purchases', label: 'المشتريات', icon: Truck },
    { id: 'inventory', label: 'المخزون', icon: BarChart3 },
    { id: 'customers', label: 'العملاء', icon: Users },
    { id: 'suppliers', label: 'الموردين', icon: Truck },
    { id: 'reports', label: 'التقارير', icon: FileText },
    { id: 'expenses', label: 'المصروفات', icon: DollarSign },
    { id: 'settings', label: 'الإعدادات', icon: Settings },
  ];

  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return <Dashboard />;
      case 'products':
        return <Products />;
      case 'sales':
        return <Sales />;
      case 'purchases':
        return <div className="p-4 md:p-6"><h2 className="text-2xl font-bold">إدارة المشتريات</h2><p className="text-gray-600 mt-4">قريباً...</p></div>;
      case 'inventory':
        return <div className="p-4 md:p-6"><h2 className="text-2xl font-bold">إدارة المخزون</h2><p className="text-gray-600 mt-4">قريباً...</p></div>;
      case 'customers':
        return <div className="p-4 md:p-6"><h2 className="text-2xl font-bold">إدارة العملاء</h2><p className="text-gray-600 mt-4">قريباً...</p></div>;
      case 'suppliers':
        return <div className="p-4 md:p-6"><h2 className="text-2xl font-bold">إدارة الموردين</h2><p className="text-gray-600 mt-4">قريباً...</p></div>;
      case 'reports':
        return <div className="p-4 md:p-6"><h2 className="text-2xl font-bold">التقارير</h2><p className="text-gray-600 mt-4">قريباً...</p></div>;
      case 'expenses':
        return <div className="p-4 md:p-6"><h2 className="text-2xl font-bold">إدارة المصروفات</h2><p className="text-gray-600 mt-4">قريباً...</p></div>;
      case 'settings':
        return <div className="p-4 md:p-6"><h2 className="text-2xl font-bold">الإعدادات</h2><p className="text-gray-600 mt-4">قريباً...</p></div>;
      default:
        return <Dashboard />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50" dir="rtl">
      <Sidebar activeTab={activeTab} setActiveTab={setActiveTab} />
      <div className="md:mr-64">
        <Header 
          activeTab={activeTab} 
          setActiveTab={setActiveTab} 
          menuItems={menuItems} 
        />
        <main className="min-h-screen">
          {renderContent()}
        </main>
      </div>
    </div>
  );
}

export default App;

