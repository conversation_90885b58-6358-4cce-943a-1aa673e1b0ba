import React, { useState } from 'react';
import { Plus, Search, Eye, Printer, Calendar, DollarSign, ShoppingCart } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const Sales = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [dateFilter, setDateFilter] = useState('all');

  // Sample sales data
  const salesInvoices = [
    {
      id: 1,
      invoiceNumber: 'INV-2024-001',
      customerName: 'أحمد محمد علي',
      date: '2024-01-15',
      items: [
        { product: 'iPhone 14 Pro', quantity: 1, price: 999 },
        { product: 'AirPods Pro', quantity: 1, price: 249 }
      ],
      totalAmount: 1248,
      status: 'مكتملة'
    },
    {
      id: 2,
      invoiceNumber: 'INV-2024-002',
      customerName: 'فاطمة أحمد',
      date: '2024-01-15',
      items: [
        { product: 'Samsung Galaxy S23', quantity: 1, price: 899 },
        { product: 'Samsung Charger', quantity: 1, price: 29 }
      ],
      totalAmount: 928,
      status: 'مكتملة'
    },
    {
      id: 3,
      invoiceNumber: 'INV-2024-003',
      customerName: 'محمد سالم',
      date: '2024-01-14',
      items: [
        { product: 'MacBook Air M2', quantity: 1, price: 1299 }
      ],
      totalAmount: 1299,
      status: 'مكتملة'
    },
    {
      id: 4,
      invoiceNumber: 'INV-2024-004',
      customerName: 'سارة محمود',
      date: '2024-01-14',
      items: [
        { product: 'iPad Pro 12.9', quantity: 1, price: 1199 },
        { product: 'Apple Pencil', quantity: 1, price: 129 }
      ],
      totalAmount: 1328,
      status: 'مكتملة'
    },
    {
      id: 5,
      invoiceNumber: 'INV-2024-005',
      customerName: 'عبدالله أحمد',
      date: '2024-01-13',
      items: [
        { product: 'Apple Watch Series 8', quantity: 2, price: 399 }
      ],
      totalAmount: 798,
      status: 'مكتملة'
    }
  ];

  const filteredInvoices = salesInvoices.filter(invoice => {
    const matchesSearch = invoice.invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         invoice.customerName.toLowerCase().includes(searchTerm.toLowerCase());
    // Add date filtering logic here if needed
    return matchesSearch;
  });

  const totalSales = salesInvoices.reduce((sum, invoice) => sum + invoice.totalAmount, 0);
  const todaySales = salesInvoices
    .filter(invoice => invoice.date === '2024-01-15')
    .reduce((sum, invoice) => sum + invoice.totalAmount, 0);

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">إدارة المبيعات</h1>
        <Button className="bg-green-600 hover:bg-green-700">
          <Plus className="w-4 h-4 ml-2" />
          فاتورة بيع جديدة
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-100">مبيعات اليوم</p>
                <p className="text-3xl font-bold">${todaySales.toLocaleString()}</p>
                <p className="text-sm text-green-100">{salesInvoices.filter(inv => inv.date === '2024-01-15').length} فاتورة</p>
              </div>
              <DollarSign className="w-12 h-12 text-green-200" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-100">إجمالي المبيعات</p>
                <p className="text-3xl font-bold">${totalSales.toLocaleString()}</p>
                <p className="text-sm text-blue-100">{salesInvoices.length} فاتورة</p>
              </div>
              <ShoppingCart className="w-12 h-12 text-blue-200" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-purple-500 to-purple-600 text-white">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-100">متوسط الفاتورة</p>
                <p className="text-3xl font-bold">${Math.round(totalSales / salesInvoices.length).toLocaleString()}</p>
                <p className="text-sm text-purple-100">لكل فاتورة</p>
              </div>
              <Calendar className="w-12 h-12 text-purple-200" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="w-5 h-5 absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="البحث برقم الفاتورة أو اسم العميل..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-4 pr-10 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                dir="rtl"
              />
            </div>
            <div className="flex gap-2">
              <select
                value={dateFilter}
                onChange={(e) => setDateFilter(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">جميع التواريخ</option>
                <option value="today">اليوم</option>
                <option value="week">هذا الأسبوع</option>
                <option value="month">هذا الشهر</option>
              </select>
              <Button variant="outline">
                <Calendar className="w-4 h-4 ml-2" />
                تصفية
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Sales Table */}
      <Card>
        <CardHeader>
          <CardTitle className="text-right">فواتير البيع ({filteredInvoices.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full text-right">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-right py-3 px-4 font-medium text-gray-700">رقم الفاتورة</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-700">العميل</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-700">التاريخ</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-700">عدد الأصناف</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-700">المبلغ الإجمالي</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-700">الحالة</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-700">الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {filteredInvoices.map((invoice) => (
                  <tr key={invoice.id} className="border-b border-gray-100 hover:bg-gray-50">
                    <td className="py-4 px-4">
                      <div>
                        <p className="font-medium text-blue-600">{invoice.invoiceNumber}</p>
                        <p className="text-sm text-gray-500">#{invoice.id}</p>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <p className="font-medium">{invoice.customerName}</p>
                    </td>
                    <td className="py-4 px-4 text-gray-600">
                      {new Date(invoice.date).toLocaleDateString('ar-SA')}
                    </td>
                    <td className="py-4 px-4 text-center">
                      <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm">
                        {invoice.items.length}
                      </span>
                    </td>
                    <td className="py-4 px-4">
                      <span className="font-bold text-green-600">
                        ${invoice.totalAmount.toLocaleString()}
                      </span>
                    </td>
                    <td className="py-4 px-4">
                      <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-sm">
                        {invoice.status}
                      </span>
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex gap-2">
                        <Button variant="ghost" size="sm" title="عرض التفاصيل">
                          <Eye className="w-4 h-4" />
                        </Button>
                        <Button variant="ghost" size="sm" title="طباعة">
                          <Printer className="w-4 h-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Quick Sale Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="text-right">ملخص سريع</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <p className="text-2xl font-bold text-green-600">{salesInvoices.length}</p>
              <p className="text-sm text-gray-600">إجمالي الفواتير</p>
            </div>
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <p className="text-2xl font-bold text-blue-600">
                {salesInvoices.reduce((sum, inv) => sum + inv.items.reduce((itemSum, item) => itemSum + item.quantity, 0), 0)}
              </p>
              <p className="text-sm text-gray-600">إجمالي القطع المباعة</p>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <p className="text-2xl font-bold text-purple-600">
                ${Math.round(totalSales / salesInvoices.length)}
              </p>
              <p className="text-sm text-gray-600">متوسط قيمة الفاتورة</p>
            </div>
            <div className="text-center p-4 bg-orange-50 rounded-lg">
              <p className="text-2xl font-bold text-orange-600">
                {new Set(salesInvoices.map(inv => inv.customerName)).size}
              </p>
              <p className="text-sm text-gray-600">عدد العملاء</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Sales;

