import React from 'react';
import { TrendingUp, Package, ShoppingCart, Users, AlertTriangle, DollarSign } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell } from 'recharts';

const Dashboard = () => {
  // Sample data for charts
  const salesData = [
    { name: 'يناير', sales: 4000, purchases: 2400 },
    { name: 'فبراير', sales: 3000, purchases: 1398 },
    { name: 'مارس', sales: 2000, purchases: 9800 },
    { name: 'أبريل', sales: 2780, purchases: 3908 },
    { name: 'مايو', sales: 1890, purchases: 4800 },
    { name: 'يونيو', sales: 2390, purchases: 3800 },
  ];

  const topProducts = [
    { name: 'iPhone 14 Pro', sales: 120, color: '#3B82F6' },
    { name: 'Samsung Galaxy S23', sales: 98, color: '#10B981' },
    { name: 'MacBook Air M2', sales: 76, color: '#F59E0B' },
    { name: 'iPad Pro', sales: 54, color: '#EF4444' },
  ];

  const recentActivities = [
    { id: 1, action: 'بيع منتج', product: 'iPhone 14 Pro', user: 'أحمد محمد', time: 'منذ 5 دقائق' },
    { id: 2, action: 'إضافة مخزون', product: 'Samsung Galaxy S23', user: 'فاطمة علي', time: 'منذ 15 دقيقة' },
    { id: 3, action: 'فاتورة شراء', product: 'MacBook Air M2', user: 'محمد أحمد', time: 'منذ 30 دقيقة' },
    { id: 4, action: 'تحديث سعر', product: 'iPad Pro', user: 'سارة محمود', time: 'منذ ساعة' },
  ];

  const lowStockItems = [
    { name: 'AirPods Pro', stock: 3, minStock: 10 },
    { name: 'Apple Watch Series 8', stock: 5, minStock: 15 },
    { name: 'iPad Mini', stock: 2, minStock: 8 },
  ];

  return (
    <div className="p-4 md:p-6 space-y-4 md:space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
        <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
          <CardContent className="p-4 md:p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-100 text-sm">إجمالي المبيعات</p>
                <p className="text-2xl md:text-3xl font-bold">$12,345</p>
                <p className="text-xs md:text-sm text-blue-100">+12% من الشهر الماضي</p>
              </div>
              <TrendingUp className="w-8 h-8 md:w-12 md:h-12 text-blue-200" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white">
          <CardContent className="p-4 md:p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-100 text-sm">عدد المنتجات</p>
                <p className="text-2xl md:text-3xl font-bold">942</p>
                <p className="text-xs md:text-sm text-green-100">+5 منتجات جديدة</p>
              </div>
              <Package className="w-8 h-8 md:w-12 md:h-12 text-green-200" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-purple-500 to-purple-600 text-white">
          <CardContent className="p-4 md:p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-100 text-sm">عدد العملاء</p>
                <p className="text-2xl md:text-3xl font-bold">1,234</p>
                <p className="text-xs md:text-sm text-purple-100">+8% نمو</p>
              </div>
              <Users className="w-8 h-8 md:w-12 md:h-12 text-purple-200" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
          <CardContent className="p-4 md:p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-orange-100 text-sm">المصروفات</p>
                <p className="text-2xl md:text-3xl font-bold">$3,456</p>
                <p className="text-xs md:text-sm text-orange-100">هذا الشهر</p>
              </div>
              <DollarSign className="w-8 h-8 md:w-12 md:h-12 text-orange-200" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 md:gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-right text-lg md:text-xl">إحصائيات المبيعات والمشتريات</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={250}>
              <LineChart data={salesData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Line type="monotone" dataKey="sales" stroke="#3B82F6" strokeWidth={2} name="المبيعات" />
                <Line type="monotone" dataKey="purchases" stroke="#10B981" strokeWidth={2} name="المشتريات" />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-right text-lg md:text-xl">أفضل المنتجات مبيعاً</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={250}>
              <BarChart data={topProducts} layout="horizontal">
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis type="number" />
                <YAxis dataKey="name" type="category" width={100} />
                <Tooltip />
                <Bar dataKey="sales" fill="#3B82F6" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activities and Alerts */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 md:gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-right text-lg md:text-xl">الأنشطة الحديثة</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 md:space-y-4">
              {recentActivities.map((activity) => (
                <div key={activity.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="text-right">
                    <p className="font-medium text-sm md:text-base">{activity.action}</p>
                    <p className="text-sm text-gray-600">{activity.product}</p>
                    <p className="text-xs text-gray-500">{activity.user} • {activity.time}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-right flex items-center gap-2 text-lg md:text-xl">
              <AlertTriangle className="w-5 h-5 text-orange-500" />
              تنبيهات المخزون المنخفض
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 md:space-y-4">
              {lowStockItems.map((item, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-orange-50 border border-orange-200 rounded-lg">
                  <div className="text-right">
                    <p className="font-medium text-sm md:text-base">{item.name}</p>
                    <p className="text-sm text-gray-600">
                      المخزون الحالي: {item.stock} | الحد الأدنى: {item.minStock}
                    </p>
                  </div>
                  <AlertTriangle className="w-5 h-5 text-orange-500" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Dashboard;

