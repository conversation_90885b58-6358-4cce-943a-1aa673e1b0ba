import React from 'react';
import { Search, Bell, User } from 'lucide-react';
import { Button } from '@/components/ui/button';
import MobileMenu from './MobileMenu';

const Header = ({ activeTab, setActiveTab, menuItems }) => {
  return (
    <header className="bg-white border-b border-gray-200 px-4 md:px-6 py-4 md:mr-64">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <MobileMenu 
            activeTab={activeTab} 
            setActiveTab={setActiveTab} 
            menuItems={menuItems} 
          />
          <div className="relative hidden sm:block">
            <Search className="w-5 h-5 absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="البحث..."
              className="pl-4 pr-10 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 w-60 md:w-80"
              dir="rtl"
            />
          </div>
        </div>
        
        <div className="flex items-center gap-2 md:gap-4">
          <Button variant="ghost" size="sm" className="relative">
            <Bell className="w-5 h-5" />
            <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full text-xs"></span>
          </Button>
          
          <div className="flex items-center gap-3">
            <div className="text-right hidden sm:block">
              <p className="text-sm font-medium">أحمد محمد</p>
              <p className="text-xs text-gray-500">مدير النظام</p>
            </div>
            <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
              <User className="w-4 h-4 text-white" />
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;

